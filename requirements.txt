# Core Data Science Libraries
pandas>=1.5.0
numpy>=1.21.0

# Machine Learning Libraries
scikit-learn>=1.1.0
xgboost>=1.6.0
imbalanced-learn>=0.9.0

# Data Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Statistical Analysis
scipy>=1.9.0

# Model Persistence
joblib>=1.1.0

# Jupyter Notebook Support
jupyter>=1.0.0
ipykernel>=6.0.0

# Optional: Google Colab Integration (if running on Colab)
# google-colab

# Development and Utilities
warnings  # Built-in Python module, no installation needed

# Additional useful libraries for data science projects
plotly>=5.0.0  # Interactive visualizations
openpyxl>=3.0.0  # Excel file support
xlrd>=2.0.0  # Excel file reading
